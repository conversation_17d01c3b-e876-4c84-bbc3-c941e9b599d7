# 图表功能修改说明

## 修改概述
根据需求，对监控数据图表功能进行了以下修改：

1. **图表显示逻辑调整**：
   - 价格图表：重点突出显示平均值
   - 库存图表：重点突出显示最差值（最小值）

2. **时间范围选择增强**：
   - 新增"今天"选项
   - 新增"昨天"选项  
   - 将默认选择改为"今天"

## 具体修改内容

### 1. 时间范围枚举修改 (ChartDataModel.kt)

**文件位置**: `app/src/main/java/dev/pigmomo/yhkit2025/ui/model/ChartDataModel.kt`

**修改内容**:
- 在TimeRange枚举中新增了TODAY("今天", 0)和YESTERDAY("昨天", -1)选项
- 将ALL_TIME的days值改为-2以区分特殊处理
- 将默认时间范围从LAST_24_HOURS改为TODAY

### 2. 时间过滤逻辑修改 (MonitoringDataViewModel.kt)

**文件位置**: `app/src/main/java/dev/pigmomo/yhkit2025/viewmodel/MonitoringDataViewModel.kt`

**修改内容**:
- 重写了`filterRecordsByTimeRange`方法，增加对TODAY和YESTERDAY的特殊处理
- TODAY：从今天00:00:00开始的所有记录
- YESTERDAY：从昨天00:00:00到昨天23:59:59的记录
- 保持其他时间范围的原有逻辑

### 3. 图表统计信息显示修改 (MonitoringChart.kt)

**文件位置**: `app/src/main/java/dev/pigmomo/yhkit2025/ui/components/MonitoringChart.kt`

**修改内容**:
- 根据数据类型区分显示逻辑：
  - **价格图表**：显示最小值、最大值、平均值，重点突出平均值
  - **库存图表**：显示最差值（最小值）、最大值、平均值，重点突出最差值
- 修改StatisticItem组件，增加isHighlighted参数用于高亮显示重要数值
- 调整数值格式化：价格保留2位小数，库存整数显示

### 4. 默认配置修改 (MonitoringDataScreen.kt)

**文件位置**: `app/src/main/java/dev/pigmomo/yhkit2025/ui/screens/MonitoringDataScreen.kt`

**修改内容**:
- 将图表默认时间范围从LAST_24_HOURS改为TODAY

## 功能特点

### 价格图表
- 重点关注平均价格，帮助用户了解商品的平均价格水平
- 平均值以粗体高亮显示
- 价格数值保留2位小数，提供精确的价格信息

### 库存图表  
- 重点关注最差库存情况（最小值），帮助用户识别库存紧张时段
- 最差值以粗体高亮显示，便于快速识别
- 库存数值显示为整数，符合实际库存计数习惯

### 时间选择
- "今天"：显示当天从00:00:00开始的所有监控数据
- "昨天"：显示昨天全天（00:00:00-23:59:59）的监控数据
- 默认选择"今天"，方便用户查看当日最新情况
- 保留原有的其他时间范围选项（最近24小时、7天、30天、90天、全部）

## 用户体验改进

1. **更直观的数据展示**：根据数据类型突出显示最重要的统计值
2. **便捷的时间选择**：新增的"今天"和"昨天"选项让用户能快速查看特定日期的数据
3. **合理的默认设置**：默认显示今天的数据，符合用户查看最新情况的习惯
4. **清晰的视觉层次**：通过高亮显示重要数值，提升信息获取效率

## 技术实现要点

1. **时间处理**：使用Calendar类精确处理日期边界，确保"今天"和"昨天"的时间范围准确
2. **条件渲染**：根据数据类型动态调整统计信息的显示内容和格式
3. **向后兼容**：保持原有功能不变，只是增强了显示效果和选项
4. **性能优化**：时间过滤逻辑高效，不影响图表渲染性能
