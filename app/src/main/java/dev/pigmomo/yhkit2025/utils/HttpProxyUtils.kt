package dev.pigmomo.yhkit2025.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Authenticator
import okhttp3.Credentials
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit

/**
 * 代理工具类，用于获取和管理HTTP代理
 */
object HttpProxyUtils {
    private const val TAG = "HttpProxyUtils"

    /**
     * 当前使用的代理
     */
    var currentProxy: Proxy? = null

    /**
     * 当前代理IP和端口
     */
    var currentProxyInfo: Pair<String?, Int?>? = null

    /**
     * 当前代理账号和密码（用于需要认证的代理）
     */
    var currentProxyAccount: Pair<String?, String?>? = null

    /**
     * 代理配置结果数据类
     */
    data class ProxyConfig(
        val proxyInfo: Pair<String?, Int?>,
        val proxyAccount: Pair<String?, String?>? = null
    )

    /**
     * IP信息数据类
     */
    data class IpInfo(
        val ip: String = "",
        val country: String? = null,
        val region: String? = null,
        val city: String? = null,
        val org: String? = null,
        val error: String? = null
    ) {
        override fun toString(): String {
            if (error != null) {
                return "Error: $error"
            }
            return "IP: $ip, Country: $country, Region: $region, City: $city, Org: $org"
        }
    }

    /**
     * OkHttpClient实例，配置超时时间
     */
    private val client by lazy {
        OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build()
    }

    /**
     * 从不同代理服务商获取代理IP
     * @param minute 代理有效时间（分钟）
     * @param inputUrl 自定义代理获取URL
     * @param selectProxyConfig 代理服务商配置类型
     * @return ProxyConfig对象，包含代理IP、端口和账户信息
     */
    suspend fun getProxyIp(
        minute: String = "1",
        inputUrl: String = "",
        selectProxyConfig: String = "pinzan"
    ): ProxyConfig = withContext(Dispatchers.IO) {
        try {
            val url = when {
                inputUrl.isNotEmpty() -> inputUrl
                selectProxyConfig == "pinzan" -> "https://service.ipzan.com/core-extract?num=1&no=20240515728138588431&minute=$minute&format=json&area=510000&repeat=1&protocol=1&pool=quality&mode=whitelist&secret=7dkovh52kered2g"
                selectProxyConfig == "shanchen" -> "https://sch.shanchendaili.com/api.html?action=get_ip&key=HU4f130cf17898652414uPG9&time=1&count=1&type=json&province=1353&only=1"
                selectProxyConfig == "ipcola_global" -> "https://api.ipcola.com/api/proxy/get_proxy_list?token=sgydwqigponkiowb8b4ea05a8e583797&num=1&type=residential&return_type=json"
                selectProxyConfig == "oxylabs_global" -> {
                    val currentTimestamp = System.currentTimeMillis().toString()
                    // 直接返回预设的代理信息，无需发送HTTP请求
                    val proxyAccount = Pair(
                        "customer-F5u41LBhW_iwMxd-sessid-$currentTimestamp-sesstime-3",
                        "n0Y_WN62lX_snSQ_"
                    )
                    return@withContext ProxyConfig(
                        proxyInfo = Pair("pr.oxylabs.io", 7777),
                        proxyAccount = proxyAccount
                    )
                }

                else -> {
                    Log.e(TAG, "Invalid proxy config: $selectProxyConfig")
                    return@withContext ProxyConfig(Pair(null, null))
                }
            }

            Log.d(TAG, "Fetching proxy from URL: $url")

            val request = Request.Builder()
                .url(url)
                .build()

            val response = client.newCall(request).execute()
            val responseBody = response.body?.string()

            if (responseBody == null) {
                Log.e(TAG, "Empty response body from proxy service")
                return@withContext ProxyConfig(Pair(null, null))
            }

            Log.d(TAG, "Proxy service response: $responseBody")

            val data = JSONObject(responseBody)

            val result = when (selectProxyConfig) {
                "pinzan" -> {
                    try {
                        val proxyList = data.getJSONObject("data").getJSONArray("list")
                        if (proxyList.length() > 0) {
                            val proxy = proxyList.getJSONObject(0)
                            val ip = proxy.getString("ip")
                            val port = proxy.getInt("port")
                            val expired = proxy.getString("expired")
                            val net = proxy.getString("net")

                            Log.d(
                                TAG,
                                "Pinzan proxy: IP=$ip, port=$port, expired=$expired, net=$net"
                            )
                            ProxyConfig(Pair(ip, port))
                        } else {
                            Log.e(TAG, "Pinzan proxy list is empty")
                            ProxyConfig(Pair(null, null))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing Pinzan proxy response: ${e.message}")
                        ProxyConfig(Pair(null, null))
                    }
                }

                "shanchen" -> {
                    try {
                        val proxyList = data.getJSONArray("list")
                        if (proxyList.length() > 0) {
                            val proxy = proxyList.getJSONObject(0)
                            val ip = proxy.getString("sever")
                            val port = proxy.getInt("port")

                            Log.d(TAG, "Shanchen proxy: IP=$ip, port=$port")
                            ProxyConfig(Pair(ip, port))
                        } else {
                            Log.e(TAG, "Shanchen proxy list is empty")
                            ProxyConfig(Pair(null, null))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing Shanchen proxy response: ${e.message}")
                        ProxyConfig(Pair(null, null))
                    }
                }

                "ipcola_global" -> {
                    try {
                        val proxyList = data.getJSONArray("proxy")
                        if (proxyList.length() > 0) {
                            val proxy = proxyList.getJSONObject(0)
                            val ip = proxy.getString("address")
                            val port = proxy.getInt("port")

                            Log.d(TAG, "IPCola proxy: IP=$ip, port=$port")
                            ProxyConfig(Pair(ip, port))
                        } else {
                            Log.e(TAG, "IPCola proxy list is empty")
                            ProxyConfig(Pair(null, null))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing IPCola proxy response: ${e.message}")
                        ProxyConfig(Pair(null, null))
                    }
                }

                else -> {
                    Log.e(TAG, "Unknown proxy config: $selectProxyConfig")
                    ProxyConfig(Pair(null, null))
                }
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "Error getting proxy: ${e.message}", e)
            ProxyConfig(Pair(null, null))
        }
    }

    /**
     * 创建并配置HTTP代理
     * @param proxyInfo 代理IP和端口信息
     * @return 配置好的Proxy对象，如果配置失败则返回null
     */
    suspend fun setupProxy(proxyInfo: Pair<String?, Int?>?): Proxy? = withContext(Dispatchers.IO) {
        if (proxyInfo?.first == null || proxyInfo.second == null) {
            Log.e(TAG, "Proxy info is incomplete, cannot configure proxy")
            return@withContext null
        }

        try {
            // 设置当前代理 Proxy 对象
            val proxy = Proxy(
                Proxy.Type.HTTP,
                InetSocketAddress(proxyInfo.first!!, proxyInfo.second!!)
            )
            currentProxy = proxy

            // 设置当前代理IP和端口信息
            currentProxyInfo = proxyInfo

            Log.d(TAG, "Proxy configured successfully: ${proxyInfo.first}:${proxyInfo.second}")
            return@withContext proxy
        } catch (e: Exception) {
            Log.e(TAG, "Error configuring proxy: ${e.message}", e)
            return@withContext null
        }
    }

    /**
     * 清除当前代理配置
     */
    fun clearProxy() {
        currentProxy = null
        currentProxyInfo = null
        currentProxyAccount = null
        Log.d(TAG, "Proxy configuration cleared")
    }

    /**
     * 获取当前IP信息，用于检测代理是否生效
     * @param currentProxyInfo 代理IP和端口，默认为null
     * @param currentProxyAccount 代理账号和密码，默认为null
     * @return IP信息的JSON字符串
     */
    suspend fun getProxyIpInfo(
        currentProxyInfo: Pair<String?, Int?> = Pair(null, null),
        currentProxyAccount: Pair<String?, String?>? = Pair(null, null),
    ): IpInfo = withContext(Dispatchers.IO) {
        try {
            // 配置代理认证
            val proxyAuthenticator = currentProxyAccount?.let {
                if (it.first != null) {
                    Authenticator { _, response ->
                        val credential = Credentials.basic(
                            it.first!!,
                            it.second!!
                        )
                        response.request.newBuilder()
                            .header("Proxy-Authorization", credential)
                            .build()
                    }
                } else {
                    Authenticator.NONE
                }
            } ?: Authenticator.NONE

            // 构建OkHttpClient，配置代理和认证
            val client = OkHttpClient.Builder()
                .apply {
                    if (currentProxyInfo.first != null && currentProxyInfo.second != null) {
                        val proxy = Proxy(
                            Proxy.Type.HTTP,
                            InetSocketAddress(
                                currentProxyInfo.first,
                                currentProxyInfo.second!!
                            )
                        )
                        proxy(proxy)
                        proxyAuthenticator(proxyAuthenticator)
                        Log.d(
                            TAG,
                            "Configuring client with proxy: ${currentProxyInfo.first}:${currentProxyInfo.second}"
                        )
                    } else {
                        Log.d(TAG, "No proxy configured for IP check")
                    }
                }
                .connectTimeout(15, TimeUnit.SECONDS)
                .readTimeout(15, TimeUnit.SECONDS)
                .writeTimeout(15, TimeUnit.SECONDS)
                .build()

            // 构建请求
            val request = Request.Builder()
                .url("https://ipinfo.io/json")
                .header(
                    "User-Agent",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                )
                .build()

            try {
                // 执行请求
                val response = client.newCall(request).execute()
                val responseBody = response.body?.string()

                if (responseBody.isNullOrEmpty()) {
                    Log.e(TAG, "Empty response from IP info service")
                    return@withContext IpInfo(error = "Empty response")
                }

                Log.d(TAG, "IP info check result: $responseBody")
                val jsonObj = JSONObject(responseBody)

                if (jsonObj.has("error")) {
                    IpInfo(
                        error = jsonObj.optString(
                            "error",
                            "Failed to get IP info from ipinfo.io"
                        )
                    )
                } else {
                    IpInfo(
                        ip = jsonObj.optString("ip"),
                        country = jsonObj.optString("country"),
                        region = jsonObj.optString("region"),
                        city = jsonObj.optString("city"),
                        org = jsonObj.optString("org")
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during IP check request: ${e.message}", e)
                // 出错时，返回 代理IP:端口，避免由于检测当前IP导致的错误
                IpInfo(
                    ip = currentProxyInfo.first + ":" + currentProxyInfo.second,
                    error = "Request failed: ${e.message}"
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking IP info: ${e.message}", e)
            IpInfo(error = "${e.message}")
        }
    }
} 