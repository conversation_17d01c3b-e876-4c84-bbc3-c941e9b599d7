package dev.pigmomo.yhkit2025.service.productmonitor

import android.util.Log
import dev.pigmomo.yhkit2025.api.RequestResult
import dev.pigmomo.yhkit2025.api.RequestService
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.cart.CartResponse
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailResponse
import dev.pigmomo.yhkit2025.api.utils.ResponseParserUtils
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.utils.productmonitor.MonitoringRequestServiceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first

/**
 * 商品处理结果
 */
data class ProductProcessResult(
    val product: ProductMonitorEntity?,
    val changes: List<ProductChangeRecordEntity>,
    val importantChanges: Int
)

/**
 * 购物车处理结果
 */
data class CartProcessResult(
    val changesDetected: Int,
    val importantChanges: Int,
    val productChanges: Map<ProductMonitorEntity, List<ProductChangeRecordEntity>>
)

/**
 * 监控任务执行结果
 */
data class MonitoringTaskResult(
    val success: Boolean,
    val message: String,
    val data: Any? = null,
    val changesDetected: Int = 0,
    val importantChanges: Int = 0,
    val productChanges: Map<ProductMonitorEntity, List<ProductChangeRecordEntity>> = emptyMap()
)

/**
 * 监控任务执行器
 * 根据监控计划类型执行对应的API请求并存储结果
 */
class MonitoringTaskExecutor(
    private val productMonitorRepository: ProductMonitorRepository,
    private val monitoringPlanRepository: MonitoringPlanRepository
) {

    companion object {
        private const val TAG = "MonitoringTaskExecutor"
    }

    /**
     * 执行监控任务
     * @param plan 监控计划
     * @return 执行结果
     */
    suspend fun executeTask(plan: MonitoringPlanEntity): MonitoringTaskResult {
        Log.d(TAG, "开始执行监控任务: ${plan.name}, 类型: ${plan.type}")

        return try {
            // 验证监控计划配置
            val (isValid, validationMessage) = MonitoringRequestServiceUtils.validateRequestServiceConfig(
                plan
            )
            if (!isValid) {
                Log.e(TAG, "监控计划配置无效: $validationMessage")
                return MonitoringTaskResult(
                    success = false,
                    message = "配置验证错误: $validationMessage"
                )
            }

            // 创建RequestService
            val requestService = MonitoringRequestServiceUtils.createRequestService(plan)

            // 根据监控类型执行对应的任务
            val result = when (plan.type) {
                MonitoringType.CART -> executeCartMonitoring(plan, requestService)
                MonitoringType.PRODUCT_DETAIL -> executeProductDetailMonitoring(
                    plan,
                    requestService
                )
            }

            // 更新监控计划的执行记录
            if (result.success) {
                // 确保数据库更新操作完成
                val updateTimeSuccess = monitoringPlanRepository.updateLastExecutedAt(plan.id)
                val updateCountSuccess = monitoringPlanRepository.incrementExecutedCount(plan.id)

                if (updateTimeSuccess && updateCountSuccess) {
                    Log.d(TAG, "监控任务执行成功，数据库更新完成: ${plan.name}")
                } else {
                    Log.w(TAG, "监控任务执行成功，但数据库更新失败: ${plan.name}")
                }
            } else {
                Log.e(TAG, "监控任务执行错误: ${plan.name}, 原因: ${result.message}")
            }

            // 发送任务执行完成事件
            Log.d(TAG, "发送任务执行完成事件: ${plan.name}, 变化数量: ${result.changesDetected}, 商品变化数量: ${result.productChanges.size}")
            MonitoringEventBus.emitTaskExecuted(plan.id, result)

            result

        } catch (e: Exception) {
            Log.e(TAG, "执行监控任务时发生异常: ${plan.name}", e)
            MonitoringTaskResult(
                success = false,
                message = "执行异常: ${e.message}"
            )
        }
    }

    /**
     * 执行购物车监控
     * @param plan 监控计划
     * @param requestService RequestService实例
     * @return 执行结果
     */
    private suspend fun executeCartMonitoring(
        plan: MonitoringPlanEntity,
        requestService: RequestService
    ): MonitoringTaskResult {
        Log.d(TAG, "执行购物车监控: ${plan.name}")

        return try {
            // 调用API获取购物车数据
            val cartResult = withContext(Dispatchers.IO) {
                requestService.cart.getAllCart(plan.addressId)
            }

            // 处理购物车结果
            when (cartResult) {
                is RequestResult.Success -> {
                    val cartResponse = ResponseParserUtils.parseCartResponse(cartResult.data)
                    if (cartResponse != null && cartResponse.code == 0) {
                        // 处理购物车数据
                        val processResult = processCartData(plan, cartResponse)

                        MonitoringTaskResult(
                            success = true,
                            message = "购物车监控完成",
                            data = cartResponse,
                            changesDetected = processResult.changesDetected,
                            importantChanges = processResult.importantChanges,
                            productChanges = processResult.productChanges
                        )
                    } else {
                        MonitoringTaskResult(
                            success = false,
                            message = "购物车API返回错误: ${cartResponse?.message ?: "未知错误"}"
                        )
                    }
                }

                is RequestResult.Error -> {
                    MonitoringTaskResult(
                        success = false,
                        message = "购物车API调用失败: ${cartResult.error.message ?: "网络错误"}"
                    )
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "购物车监控执行异常", e)
            MonitoringTaskResult(
                success = false,
                message = "购物车监控异常: ${e.message}"
            )
        }
    }

    /**
     * 执行商品详情监控
     * @param plan 监控计划
     * @param requestService RequestService实例
     * @return 执行结果
     */
    private suspend fun executeProductDetailMonitoring(
        plan: MonitoringPlanEntity,
        requestService: RequestService
    ): MonitoringTaskResult {
        Log.d(TAG, "执行商品详情监控: ${plan.name}")

        return try {
            var totalChanges = 0
            var totalImportantChanges = 0
            val results = mutableListOf<ProductDetailResponse>()
            val productChangesMap = mutableMapOf<ProductMonitorEntity, List<ProductChangeRecordEntity>>()

            // 遍历所有需要监控的商品
            for (productCode in plan.productIds) {
                try {
                    // 调用商品详情API
                    val result = withContext(Dispatchers.IO) {
                        requestService.product.getSkuDetail(code = productCode)
                    }

                    when (result) {
                        is RequestResult.Success -> {
                            val productDetailResponse =
                                ResponseParserUtils.parseProductDetailResponse(result.data)
                            if (productDetailResponse != null && productDetailResponse.code == 0 && productDetailResponse.data != null) {
                                results.add(productDetailResponse)

                                // 处理商品详情数据
                                val processResult =
                                    processProductDetailData(plan, productDetailResponse.data)
                                totalChanges += processResult.changes.size
                                totalImportantChanges += processResult.importantChanges

                                // 如果有变化且有商品信息，添加到变化映射中
                                if (processResult.changes.isNotEmpty() && processResult.product != null) {
                                    productChangesMap[processResult.product] = processResult.changes
                                }

                                Log.d(
                                    TAG,
                                    "商品 $productCode 监控完成，检测到 ${processResult.changes.size} 个变化"
                                )
                            } else {
                                Log.w(
                                    TAG,
                                    "商品 $productCode API返回错误: ${productDetailResponse?.message}"
                                )
                            }
                        }

                        is RequestResult.Error -> {
                            Log.w(TAG, "商品 $productCode API调用失败: ${result.error.message}")
                        }
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "监控商品 $productCode 时发生异常", e)
                }
            }

            MonitoringTaskResult(
                success = true,
                message = "商品详情监控完成，处理了 ${results.size}/${plan.productIds.size} 个商品",
                data = results,
                changesDetected = totalChanges,
                importantChanges = totalImportantChanges,
                productChanges = productChangesMap
            )

        } catch (e: Exception) {
            Log.e(TAG, "商品详情监控执行异常", e)
            MonitoringTaskResult(
                success = false,
                message = "商品详情监控异常: ${e.message}"
            )
        }
    }

    /**
     * 处理购物车数据
     * @param plan 监控计划
     * @param cartResponse 购物车响应数据
     * @return CartProcessResult 包含变化数量和商品变化信息
     */
    private suspend fun processCartData(
        plan: MonitoringPlanEntity,
        cartResponse: CartResponse
    ): CartProcessResult {
        var totalChanges = 0
        var importantChanges = 0
        val productChangesMap = mutableMapOf<ProductMonitorEntity, List<ProductChangeRecordEntity>>()

        try {
            // 提取购物车中的商品 - 需要从cartlist中的cartModels中提取Product类型的数据
            val cartItems = cartResponse.data?.cartlist ?: emptyList()
            val products = mutableListOf<Pair<Product, String>>() // 商品和店铺名称的配对

            // 遍历购物车项目，提取商品信息和店铺名称
            for (cartItem in cartItems) {
                val shopName = cartItem.shopname // 获取店铺名称
                for (cartModel in cartItem.cartModels) {
                    if (cartModel.modelType == CartModelTypes.PRODUCT_ITEM) {
                        val wrapper = CartModelWrapper(
                            data = cartModel.data,
                            modelType = cartModel.modelType
                        )
                        wrapper.getProduct()?.let { product ->
                            products.add(Pair(product, shopName))
                        }
                    }
                }
            }

            Log.d(TAG, "处理购物车数据，共 ${products.size} 个商品")

            // 批量处理商品监控
            for ((product, shopName) in products) {
                try {
                    // 如果监控计划指定了特定商品ID，则只处理这些商品
                    if (plan.productIds.isNotEmpty() && !plan.productIds.contains(product.id)) {
                        continue
                    }

                    // 更新商品监控数据并记录变化
                    val changes = productMonitorRepository.updateProductAndRecordChanges(product, plan.shopId, shopName)
                    totalChanges += changes.size

                    // 统计重要变化
                    val importantChangeCount = changes.count { it.isImportant }
                    importantChanges += importantChangeCount

                    // 如果有变化，获取商品信息并添加到变化映射中
                    if (changes.isNotEmpty()) {
                        try {
                            val productEntity = productMonitorRepository.getProductByIdAndShopId(product.id, plan.shopId).first()
                            if (productEntity != null) {
                                productChangesMap[productEntity] = changes
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "获取商品实体失败: ${product.id}", e)
                        }

                        Log.d(
                            TAG,
                            "商品 ${product.id} 检测到 ${changes.size} 个变化，其中 $importantChangeCount 个重要变化"
                        )
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "处理购物车商品 ${product.id} 时发生异常", e)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理购物车数据时发生异常", e)
        }

        return CartProcessResult(
            changesDetected = totalChanges,
            importantChanges = importantChanges,
            productChanges = productChangesMap
        )
    }

    /**
     * 处理商品详情数据
     * @param plan 监控计划
     * @param productDetailData 商品详情数据
     * @return ProductProcessResult 包含商品和变化信息
     */
    private suspend fun processProductDetailData(
        plan: MonitoringPlanEntity,
        productDetailData: ProductDetailData
    ): ProductProcessResult {
        return try {
            // 更新商品监控数据并记录变化
            // 对于商品详情页监控，我们没有直接的店铺名称，使用空字符串
            val changes =
                productMonitorRepository.updateProductDetailAndRecordChanges(productDetailData, plan.shopId, "")

            // 获取商品信息 - 使用first()获取Flow的第一个值
            val product = productMonitorRepository.getProductByIdAndShopId(productDetailData.id!!, plan.shopId).first()

            // 统计重要变化
            val importantChangeCount = changes.count { it.isImportant }

            if (changes.isNotEmpty()) {
                Log.d(
                    TAG,
                    "商品详情 ${productDetailData.id} 检测到 ${changes.size} 个变化，其中 $importantChangeCount 个重要变化"
                )
            }

            ProductProcessResult(
                product = product,
                changes = changes,
                importantChanges = importantChangeCount
            )

        } catch (e: Exception) {
            Log.e(TAG, "处理商品详情数据时发生异常", e)
            ProductProcessResult(
                product = null,
                changes = emptyList(),
                importantChanges = 0
            )
        }
    }

    /**
     * 批量执行监控任务
     * @param plans 监控计划列表
     * @return 执行结果列表
     */
    suspend fun executeBatchTasks(plans: List<MonitoringPlanEntity>): List<Pair<MonitoringPlanEntity, MonitoringTaskResult>> {
        Log.i(TAG, "开始批量执行 ${plans.size} 个监控任务")
        plans.forEach { plan ->
            Log.d(TAG, "准备执行任务: ${plan.name}, ID: ${plan.id}, 类型: ${plan.type}")
        }

        val results = mutableListOf<Pair<MonitoringPlanEntity, MonitoringTaskResult>>()

        for (plan in plans) {
            try {
                Log.i(TAG, "正在执行监控任务: ${plan.name}")
                val result = executeTask(plan)
                results.add(Pair(plan, result))
                Log.i(TAG, "监控任务 ${plan.name} 执行完成，结果: ${if (result.success) "成功" else "失败 - ${result.message}"}")

                // 添加延迟以避免API请求过于频繁
                delay(1000)

            } catch (e: Exception) {
                Log.e(TAG, "批量执行监控任务时发生异常: ${plan.name}", e)
                results.add(
                    Pair(
                        plan, MonitoringTaskResult(
                            success = false,
                            message = "批量执行异常: ${e.message}"
                        )
                    )
                )
            }
        }

        Log.d(
            TAG,
            "批量监控任务执行完成，成功: ${results.count { it.second.success }}/${results.size}"
        )

        // 发送批量任务执行完成事件
        val eventResults = results.map { (plan, result) ->
            Pair(plan.id, result)
        }
        MonitoringEventBus.emitBatchTasksExecuted(eventResults)

        return results
    }
}
