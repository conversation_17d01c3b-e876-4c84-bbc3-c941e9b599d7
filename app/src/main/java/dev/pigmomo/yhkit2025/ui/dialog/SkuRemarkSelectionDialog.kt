package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import dev.pigmomo.yhkit2025.api.model.cart.AddToCartData
import dev.pigmomo.yhkit2025.api.model.cart.RemarkDetail
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.PriceUtils

/**
 * SKU备注选择弹窗
 * 用于显示商品的备注选项，如宰杀方式等
 *
 * @param skuRemarkData 备注数据
 * @param onDismiss 关闭弹窗回调
 * @param onRemarkSelected 备注选择回调，参数为(备注名称, 数量)
 */
@Composable
fun SkuRemarkSelectionDialog(
    skuRemarkData: AddToCartData?,
    onDismiss: () -> Unit,
    onRemarkSelected: (String, Int) -> Unit
) {
    if (skuRemarkData?.skuRemarkInfo == null) return

    // 选中的备注和数量
    var selectedRemark by remember {
        mutableStateOf(skuRemarkData.skuRemarkInfo.remarkDetailList[0].name)
    }
    var selectedQuantity by remember { mutableIntStateOf(1) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = skuRemarkData.skuRemarkInfo.remarkName
            )
        },
        containerColor = dialogContainerColor(),
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 500.dp)
            ) {
                // 商品信息
                ProductInfoSection(skuRemarkData)

                Spacer(modifier = Modifier.height(8.dp))

                // 备注选项列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f, false),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(skuRemarkData.skuRemarkInfo.remarkDetailList) { remarkDetail ->
                        RemarkOptionItem(
                            remarkDetail = remarkDetail,
                            isSelected = selectedRemark == remarkDetail.name,
                            onSelected = {
                                selectedRemark = remarkDetail.name
                                selectedQuantity = 1 // 重置数量
                            }
                        )
                    }
                }

                // 数量选择
                Spacer(modifier = Modifier.height(8.dp))
                QuantitySelector(
                    quantity = selectedQuantity,
                    maxQuantity = 100, // 默认最大数量，可根据需要调整
                    onQuantityChange = { selectedQuantity = it }
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    onRemarkSelected(selectedRemark, selectedQuantity)
                    onDismiss()
                }
            ) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 商品信息展示区域
 */
@Composable
private fun ProductInfoSection(skuRemarkData: AddToCartData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 商品图片
            if (skuRemarkData.listimgs.isNotEmpty()) {
                AsyncImage(
                    model = skuRemarkData.listimgs.first(),
                    contentDescription = null,
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop
                )
                Spacer(modifier = Modifier.width(12.dp))
            }

            // 商品信息
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = skuRemarkData.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 价格信息
                skuRemarkData.price?.let {
                    if (it.value > 0) {
                        Text(
                            text = "¥${PriceUtils.formatPrice(skuRemarkData.price.value)}",
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }
    }
}

/**
 * 备注选项项目
 */
@Composable
private fun RemarkOptionItem(
    remarkDetail: RemarkDetail,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clickable { onSelected() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        2.dp,
                        MaterialTheme.colorScheme.primary,
                        RoundedCornerShape(8.dp)
                    )
                } else {
                    Modifier
                }
            ),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选项文本
            Text(
                text = remarkDetail.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f)
            )

            // 选中指示器
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .background(MaterialTheme.colorScheme.primary),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "✓",
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

/**
 * 数量选择器
 */
@Composable
private fun QuantitySelector(
    quantity: Int,
    maxQuantity: Int,
    onQuantityChange: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = cardThemeOverlay()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "数量",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 减少按钮
                QuantityControlButton(
                    text = "-",
                    enabled = quantity > 1,
                    onClick = {
                        if (quantity > 1) {
                            onQuantityChange(quantity - 1)
                        }
                    }
                )

                // 数量显示
                Text(
                    text = quantity.toString(),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )

                // 增加按钮
                QuantityControlButton(
                    text = "+",
                    enabled = quantity < maxQuantity,
                    onClick = {
                        if (quantity < maxQuantity) {
                            onQuantityChange(quantity + 1)
                        }
                    }
                )
            }
        }
    }
}

/**
 * 数量控制按钮组件
 * 统一的加减按钮样式
 */
@Composable
private fun QuantityControlButton(
    text: String,
    enabled: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(24.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(
                if (enabled) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.1f)
            )
            .clickable(enabled = enabled) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = if (enabled) MaterialTheme.colorScheme.primary
            else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        )
    }
}
