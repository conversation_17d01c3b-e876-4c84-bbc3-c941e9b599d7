package dev.pigmomo.yhkit2025.ui.components

import android.annotation.SuppressLint
import android.graphics.Color.parseColor
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.ui.model.ChartDataSet
import dev.pigmomo.yhkit2025.ui.model.ChartDetailInfo
import dev.pigmomo.yhkit2025.ui.model.ChartDataPoint
import dev.pigmomo.yhkit2025.ui.model.ChartDataType
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 * 监控数据图表组件
 * 用于显示价格或库存的时间序列数据
 */
@SuppressLint("DefaultLocale")
@Composable
fun MonitoringChart(
    dataSet: ChartDataSet,
    onDataPointClick: (ChartDetailInfo) -> Unit = {},
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }
    val valueFormat = remember { SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 图表标题和统计信息
            ChartHeader(dataSet = dataSet)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (dataSet.hasData) {
                // 简单的自定义图表
                SimpleLineChart(
                    dataSet = dataSet,
                    onDataPointClick = { dataPoint ->
                        val detailInfo = ChartDetailInfo(
                            dataPoint = dataPoint,
                            formattedTime = valueFormat.format(dataPoint.timestamp),
                            formattedValue = "${String.format("%.2f", dataPoint.value)} ${dataSet.config.dataType.unit}",
                            details = dataPoint.extraInfo
                        )
                        onDataPointClick(detailInfo)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(180.dp) // 稍微减少高度，为其他内容留出空间
                )
            } else {
                // 无数据状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(180.dp), // 与图表高度保持一致
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "暂无数据",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "请选择其他时间范围或等待数据更新",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

/**
 * 图表头部信息
 */
@SuppressLint("DefaultLocale")
@Composable
private fun ChartHeader(dataSet: ChartDataSet) {
    Column {
        // 标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${dataSet.config.dataType.displayName}趋势",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            if (dataSet.hasData) {
                Surface(
                    color = MaterialTheme.colorScheme.primaryContainer,
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "${dataSet.dataPointCount}个数据点",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
        }
        
        if (dataSet.hasData) {
            Spacer(modifier = Modifier.height(8.dp))
            
            // 统计信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                when (dataSet.config.dataType) {
                    ChartDataType.PRICE -> {
                        // 价格图表：显示最小值、最大值、平均值（重点突出平均值）
                        StatisticItem(
                            label = "最小值",
                            value = "${String.format("%.2f", dataSet.minValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f)
                        )
                        StatisticItem(
                            label = "最大值",
                            value = "${String.format("%.2f", dataSet.maxValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f)
                        )
                        StatisticItem(
                            label = "平均值",
                            value = "${String.format("%.2f", dataSet.averageValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f),
                            isHighlighted = true
                        )
                    }
                    ChartDataType.STOCK -> {
                        // 库存图表：显示最差值（最小值）、最大值、平均值（重点突出最差值）
                        StatisticItem(
                            label = "最差值",
                            value = "${String.format("%.0f", dataSet.minValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f),
                            isHighlighted = true
                        )
                        StatisticItem(
                            label = "最大值",
                            value = "${String.format("%.0f", dataSet.maxValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f)
                        )
                        StatisticItem(
                            label = "平均值",
                            value = "${String.format("%.1f", dataSet.averageValue)} ${dataSet.config.dataType.unit}",
                            modifier = Modifier.weight(1f)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 统计信息项
 */
@Composable
private fun StatisticItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    isHighlighted: Boolean = false
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = if (isHighlighted) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = if (isHighlighted) FontWeight.Bold else FontWeight.Normal
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isHighlighted) FontWeight.Bold else FontWeight.Medium,
            color = if (isHighlighted) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 简单的线性图表组件
 */
@Composable
private fun SimpleLineChart(
    dataSet: ChartDataSet,
    onDataPointClick: (ChartDataPoint) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val sortedData = remember(dataSet) { dataSet.dataPoints.sortedBy { it.timestamp } }
    val lineColor = Color(parseColor(dataSet.config.lineColor))

    if (sortedData.isEmpty()) return

    // 存储数据点位置，用于点击检测
    var dataPointPositions by remember { mutableStateOf<List<Offset>>(emptyList()) }

    Canvas(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(sortedData) {
                detectTapGestures { offset ->
                    // 查找最近的数据点，增大检测范围
                    val clickedPointIndex = findNearestDataPoint(offset, dataPointPositions, 80f)
                    if (clickedPointIndex >= 0 && clickedPointIndex < sortedData.size) {
                        onDataPointClick(sortedData[clickedPointIndex])
                    }
                }
            }
    ) {
        val positions = drawSimpleLineChart(
            dataPoints = sortedData,
            lineColor = lineColor,
            showDataPoints = dataSet.config.showDataPoints,
            showGridLines = dataSet.config.showGridLines,
            smoothLine = dataSet.config.smoothLine
        )
        dataPointPositions = positions
    }
}

/**
 * 绘制简单的线性图表
 * @return 数据点的位置列表，用于点击检测
 */
private fun DrawScope.drawSimpleLineChart(
    dataPoints: List<ChartDataPoint>,
    lineColor: Color,
    showDataPoints: Boolean,
    showGridLines: Boolean,
    smoothLine: Boolean
): List<Offset> {
    if (dataPoints.isEmpty()) return emptyList()

    val padding = 40f
    val chartWidth = size.width - 2 * padding
    val chartHeight = size.height - 2 * padding

    val minValue = dataPoints.minOf { it.value }
    val maxValue = dataPoints.maxOf { it.value }
    val valueRange = maxValue - minValue

    if (valueRange == 0.0) return emptyList()

    // 绘制网格线
    if (showGridLines) {
        val gridColor = Color.Gray.copy(alpha = 0.3f)

        // 水平网格线
        for (i in 0..4) {
            val y = padding + (chartHeight * i / 4)
            drawLine(
                color = gridColor,
                start = Offset(padding, y),
                end = Offset(size.width - padding, y),
                strokeWidth = 1f
            )
        }

        // 垂直网格线
        for (i in 0..4) {
            val x = padding + (chartWidth * i / 4)
            drawLine(
                color = gridColor,
                start = Offset(x, padding),
                end = Offset(x, size.height - padding),
                strokeWidth = 1f
            )
        }
    }

    // 计算数据点位置
    val points = dataPoints.mapIndexed { index, dataPoint ->
        val x = padding + (chartWidth * index / (dataPoints.size - 1))
        val y = padding + chartHeight - ((dataPoint.value - minValue) / valueRange * chartHeight).toFloat()
        Offset(x, y)
    }

    // 绘制线条
    if (points.size > 1) {
        if (smoothLine) {
            // 绘制平滑曲线（改进版）
            val path = Path()
            path.moveTo(points[0].x, points[0].y)

            for (i in 1 until points.size) {
                val prevPoint = points[i - 1]
                val currentPoint = points[i]

                // 计算控制点，使曲线更自然
                val controlDistance = (currentPoint.x - prevPoint.x) * 0.3f
                val controlPoint1 = Offset(
                    prevPoint.x + controlDistance,
                    prevPoint.y
                )
                val controlPoint2 = Offset(
                    currentPoint.x - controlDistance,
                    currentPoint.y
                )

                path.cubicTo(
                    controlPoint1.x, controlPoint1.y,
                    controlPoint2.x, controlPoint2.y,
                    currentPoint.x, currentPoint.y
                )
            }

            drawPath(
                path = path,
                color = lineColor,
                style = androidx.compose.ui.graphics.drawscope.Stroke(width = 3f)
            )
        } else {
            // 绘制直线
            for (i in 1 until points.size) {
                drawLine(
                    color = lineColor,
                    start = points[i - 1],
                    end = points[i],
                    strokeWidth = 3f
                )
            }
        }
    }

    // 绘制数据点
    if (showDataPoints) {
        points.forEach { point ->
            // 外圈（更大，便于点击）
            drawCircle(
                color = lineColor,
                radius = 8f,
                center = point
            )
            // 内圈（白色）
            drawCircle(
                color = Color.White,
                radius = 4f,
                center = point
            )
        }
    }

    // 返回数据点位置用于点击检测
    return points
}

/**
 * 查找最近的数据点
 * @param clickPosition 点击位置
 * @param dataPointPositions 数据点位置列表
 * @param maxDistance 最大检测距离
 * @return 最近数据点的索引，如果没有找到返回-1
 */
private fun findNearestDataPoint(
    clickPosition: Offset,
    dataPointPositions: List<Offset>,
    maxDistance: Float = 50f
): Int {
    var nearestIndex = -1
    var minDistance = Float.MAX_VALUE

    dataPointPositions.forEachIndexed { index, position ->
        val distance = sqrt(
            (clickPosition.x - position.x) * (clickPosition.x - position.x) +
            (clickPosition.y - position.y) * (clickPosition.y - position.y)
        )

        if (distance < minDistance && distance <= maxDistance) {
            minDistance = distance
            nearestIndex = index
        }
    }

    return nearestIndex
}
