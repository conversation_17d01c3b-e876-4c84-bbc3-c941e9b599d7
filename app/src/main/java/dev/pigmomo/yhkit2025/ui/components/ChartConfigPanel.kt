package dev.pigmomo.yhkit2025.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.ui.model.ChartConfig
import dev.pigmomo.yhkit2025.ui.model.ChartDataType
import dev.pigmomo.yhkit2025.ui.model.TimeRange

/**
 * 图表配置面板
 * 允许用户配置图表的显示参数
 */
@Composable
fun ChartConfigPanel(
    config: ChartConfig,
    onConfigChange: (ChartConfig) -> Unit,
    modifier: Modifier = Modifier
) {
    var isExpanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 配置面板标题
            ConfigPanelHeader(
                isExpanded = isExpanded,
                onToggleExpand = { isExpanded = !isExpanded }
            )
            
            // 展开的配置选项
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMediumLow
                    )
                ) + fadeIn(),
                exit = shrinkVertically(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioNoBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeOut()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 12.dp), // 减少顶部间距
                    verticalArrangement = Arrangement.spacedBy(12.dp) // 减少组件间距
                ) {
                    // 数据类型选择
                    DataTypeSelector(
                        selectedType = config.dataType,
                        onTypeChange = { newType ->
                            onConfigChange(config.copy(dataType = newType))
                        }
                    )
                    
                    // 时间范围选择
                    TimeRangeSelector(
                        selectedRange = config.timeRange,
                        onRangeChange = { newRange ->
                            onConfigChange(config.copy(timeRange = newRange))
                        }
                    )
                }
            }
        }
    }
}

/**
 * 配置面板标题
 */
@Composable
private fun ConfigPanelHeader(
    isExpanded: Boolean,
    onToggleExpand: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable { onToggleExpand() },
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = "图表配置",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = "图表配置",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        val rotationAngle by animateFloatAsState(
            targetValue = if (isExpanded) 180f else 0f,
            animationSpec = tween(
                durationMillis = 300,
                easing = FastOutSlowInEasing
            ),
            label = "expand_icon_rotation"
        )
        
        Icon(
            imageVector = Icons.Default.KeyboardArrowDown,
            contentDescription = if (isExpanded) "收起" else "展开",
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier
                .size(20.dp)
                .rotate(rotationAngle)
        )
    }
}

/**
 * 数据类型选择器
 */
@Composable
private fun DataTypeSelector(
    selectedType: ChartDataType,
    onTypeChange: (ChartDataType) -> Unit
) {
    Column {
        Text(
            text = "数据类型",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            ChartDataType.entries.forEach { type ->
                FilterChip(
                    onClick = { onTypeChange(type) },
                    label = {
                        Text(
                            text = type.displayName,
                            fontSize = 12.sp
                        )
                    },
                    selected = selectedType == type,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 时间范围选择器
 */
@Composable
private fun TimeRangeSelector(
    selectedRange: TimeRange,
    onRangeChange: (TimeRange) -> Unit
) {
    Column {
        Text(
            text = "时间范围",
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth().horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            TimeRange.entries.forEach { range ->
                FilterChip(
                    onClick = { onRangeChange(range) },
                    label = {
                        Text(
                            text = range.displayName,
                            fontSize = 11.sp
                        )
                    },
                    selected = selectedRange == range
                )
            }
        }
    }
}


