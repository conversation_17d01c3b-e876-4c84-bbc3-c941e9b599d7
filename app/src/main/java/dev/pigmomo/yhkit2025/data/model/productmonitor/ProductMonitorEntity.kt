package dev.pigmomo.yhkit2025.data.model.productmonitor

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import dev.pigmomo.yhkit2025.data.utils.DateTypeConverter
import dev.pigmomo.yhkit2025.data.utils.ProductTypeConverter
import dev.pigmomo.yhkit2025.data.utils.StringListConverter
import java.util.Date

/**
 * 商品监控实体类
 * 用于存储商品的基本信息和当前状态
 * 使用复合主键（商品ID + 店铺ID）来区分不同店铺的同一商品
 */
@Entity(
    tableName = "product_monitor",
    primaryKeys = ["id", "shop_id"]
)
@TypeConverters(DateTypeConverter::class, StringListConverter::class, ProductTypeConverter::class)
data class ProductMonitorEntity(
    /**
     * 商品ID - 复合主键之一
     */
    val id: String,

    /**
     * 原始SKU码
     */
    @ColumnInfo(name = "original_sku_code")
    val originalSkuCode: String = "",

    /**
     * 商品标题
     */
    val title: String = "",

    /**
     * 商品副标题
     */
    val subtitle: String = "",

    /**
     * 商品图片URL
     */
    @ColumnInfo(name = "img_url")
    val imgUrl: String = "",

    /**
     * 当前价格(分)
     */
    @ColumnInfo(name = "current_price")
    val currentPrice: Int = 0,

    /**
     * 市场价格(分)
     */
    @ColumnInfo(name = "market_price")
    val marketPrice: Int = 0,

    /**
     * 价格类型(如"seckill"表示秒杀价)
     */
    @ColumnInfo(name = "price_kind")
    val priceKind: String = "",

    /**
     * 库存数量
     */
    @ColumnInfo(name = "stock_num")
    val stockNum: Int = 0,

    /**
     * 是否可用 (0-不可用, 1-可用)
     */
    val available: Int = 0,

    /**
     * 是否秒杀商品 (0-否, 1-是)
     */
    @ColumnInfo(name = "is_seckill")
    val isSeckill: Int = 0,

    /**
     * 是否不可购买
     */
    @ColumnInfo(name = "can_not_buy")
    val canNotBuy: Boolean = false,

    /**
     * 店铺ID - 复合主键之一
     */
    @ColumnInfo(name = "shop_id")
    val shopId: String = "",

    /**
     * 店铺名称
     */
    @ColumnInfo(name = "shop_name")
    val shopName: String = "",

    /**
     * 分类ID
     */
    @ColumnInfo(name = "category_id")
    val categoryId: String = "",

    /**
     * 商品标签列表
     */
    @ColumnInfo(name = "product_tags")
    val productTags: List<String> = emptyList(),

    /**
     * 商品规格描述
     */
    @ColumnInfo(name = "spec_desc")
    val specDesc: String = "",

    /**
     * 限购数量
     */
    @ColumnInfo(name = "restrict_limit")
    val restrictLimit: Int = 0,

    /**
     * 限购类型
     */
    @ColumnInfo(name = "restrict_type")
    val restrictType: Int = 0,

    /**
     * 缺货备注
     */
    @ColumnInfo(name = "few_stock_remark")
    val fewStockRemark: String = "",

    /**
     * 配送时间描述
     */
    @ColumnInfo(name = "delivery_time_desc")
    val deliveryTimeDesc: String = "",

    /**
     * 是否小时达
     */
    @ColumnInfo(name = "is_performance_hour_hour")
    val isPerformanceHourHour: Boolean = false,

    /**
     * 毛重
     */
    @ColumnInfo(name = "gross_weight")
    val grossWeight: Double = 0.0,

    /**
     * 过期日期
     */
    @ColumnInfo(name = "expiration_date")
    val expirationDate: Int = 0,

    /**
     * 是否批量商品
     */
    @ColumnInfo(name = "is_bulk_item")
    val isBulkItem: Int = 0,

    /**
     * 商品标签
     */
    @ColumnInfo(name = "goods_tag")
    val goodsTag: String = "",

    /**
     * 商品标签ID
     */
    @ColumnInfo(name = "goods_tag_id")
    val goodsTagId: Int = 0,

    /**
     * 促销码
     */
    @ColumnInfo(name = "bundle_promo_code")
    val bundlePromoCode: String = "",

    /**
     * 商品备注
     */
    @ColumnInfo(name = "order_remark")
    val orderRemark: String = "",

    /**
     * 首次添加时间
     */
    @ColumnInfo(name = "first_add_time")
    val firstAddTime: Date = Date(),

    /**
     * 最后更新时间
     */
    @ColumnInfo(name = "last_update_time")
    val lastUpdateTime: Date = Date(),

    /**
     * 是否启用监控
     */
    @ColumnInfo(name = "is_monitoring_enabled")
    val isMonitoringEnabled: Boolean = true,

    /**
     * 监控备注
     */
    @ColumnInfo(name = "monitor_note")
    val monitorNote: String = ""
)