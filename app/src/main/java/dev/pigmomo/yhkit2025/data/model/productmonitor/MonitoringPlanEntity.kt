package dev.pigmomo.yhkit2025.data.model.productmonitor

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.data.utils.DateTypeConverter
import dev.pigmomo.yhkit2025.data.utils.StringListConverter
import dev.pigmomo.yhkit2025.data.utils.ProductTypeConverter
import java.util.Date

/**
 * 监控计划类型枚举
 */
enum class MonitoringType {
    /**
     * 购物车监控
     */
    CART,

    /**
     * 商品详情页监控
     */
    PRODUCT_DETAIL
}

/**
 * 监控操作类型枚举
 */
enum class MonitoringOperationType {
    /**
     * 间隔监控 - 按固定间隔时间执行
     */
    INTERVAL,

    /**
     * 定时监控 - 在指定时间点执行
     */
    SCHEDULED,

    /**
     * 手动监控 - 仅手动触发
     */
    MANUAL
}

/**
 * 监控计划实体类
 * 用于存储不同类型的监控任务信息
 */
@Entity(tableName = "monitoring_plans")
@TypeConverters(StringListConverter::class, DateTypeConverter::class, ProductTypeConverter::class)
data class MonitoringPlanEntity(
    /**
     * 监控计划唯一标识符
     * 使用自增整数作为主键
     */
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,

    /**
     * 监控计划名称
     */
    val name: String,

    /**
     * 监控类型（购物车、商品详情页）
     */
    val type: MonitoringType,

    /**
     * 嵌入完整的账户信息（OrderTokenEntity）
     * 使用@Embedded注解将OrderTokenEntity的所有字段直接嵌入到当前表中
     */
    @Embedded(prefix = "account_")
    val account: OrderTokenEntity,

    /**
     * 监控的商品ID列表（可多个）
     */
    val productIds: List<String>,

    /**
     * 是否启用监控
     */
    val isEnabled: Boolean = true,

    /**
     * 监控创建时间
     */
    val createdAt: Date = Date(),

    /**
     * 上次监控执行时间
     */
    val lastExecutedAt: Date? = null,

    /**
     * 监控操作类型（间隔、定时、手动）
     */
    val operationType: MonitoringOperationType = MonitoringOperationType.INTERVAL,

    /**
     * 监控间隔（秒）- 仅当operationType为INTERVAL时有效
     */
    val intervalSeconds: Int = 60,

    /**
     * 定时监控的时间配置（JSON格式）
     * 例如：{"hours": [9, 12, 18], "minutes": [0, 30]} 表示每天9:00, 9:30, 12:00, 12:30, 18:00, 18:30执行
     * 或者：{"cron": "0 0 9,12,18 * * ?"} 表示每天9点、12点、18点执行
     */
    val scheduledConfig: String = "",

    /**
     * 监控执行的开始时间（可选）
     * 如果设置，则只在此时间之后执行监控
     */
    val startTime: Date? = null,

    /**
     * 监控执行的结束时间（可选）
     * 如果设置，则只在此时间之前执行监控
     */
    val endTime: Date? = null,

    /**
     * 每日监控的开始时间（小时，0-23）
     * 仅在指定时间段内执行监控，-1表示不限制
     */
    val dailyStartHour: Int = -1,

    /**
     * 每日监控的结束时间（小时，0-23）
     * 仅在指定时间段内执行监控，-1表示不限制
     */
    val dailyEndHour: Int = -1,

    /**
     * 是否仅在工作日执行（周一到周五）
     */
    val weekdaysOnly: Boolean = false,

    /**
     * 最大执行次数（-1表示无限制）
     */
    val maxExecutions: Int = -1,

    /**
     * 已执行次数
     */
    val executedCount: Int = 0,

    /**
     * 监控优先级（1-10，数字越大优先级越高）
     */
    val priority: Int = 5,

    /**
     * 是否启用失败重试
     */
    val enableRetry: Boolean = true,

    /**
     * 最大重试次数
     */
    val maxRetries: Int = 3,

    /**
     * 重试间隔（秒）
     */
    val retryIntervalSeconds: Int = 30,

    /**
     * 服务类型（app、mini等）
     * 用于创建RequestService时指定服务类型
     */
    val serviceType: String = "app",

    /**
     * 地址ID
     * 用于设置RequestService的地址信息
     */
    val addressId: String = "",

    /**
     * XYH业务参数
     * 用于设置RequestService的X-YH-Biz-Params请求头参数
     */
    val xyhBizParams: String = "",

    /**
     * Web XYH业务参数
     * 用于设置RequestService的Web X-YH-Biz-Params请求头参数
     */
    val webXyhBizParams: String = "",

    /**
     * 店铺ID
     * 用于设置RequestService的店铺信息
     */
    val shopId: String = "",
    
    /**
     * 店铺名称
     * 用于显示店铺信息
     */
    val shopName: String = "",

    /**
     * 卖家ID
     * 用于设置RequestService的卖家信息
     */
    val sellerId: String = "",

    /**
     * 城市ID
     * 用于设置RequestService的城市信息
     */
    val cityId: String = "",

    /**
     * 区域信息
     * 用于设置RequestService的区域信息
     */
    val district: String = "",

    /**
     * 附加备注
     */
    val note: String = ""
)