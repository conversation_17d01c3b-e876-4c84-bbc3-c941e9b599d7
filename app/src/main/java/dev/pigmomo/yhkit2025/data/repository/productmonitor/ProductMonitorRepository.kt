package dev.pigmomo.yhkit2025.data.repository.productmonitor

import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 商品监控仓库接口
 * 定义商品监控相关的业务逻辑操作
 */
interface ProductMonitorRepository {

    /**
     * 添加或更新商品监控
     * @param product 原始商品数据
     * @param shopId 店铺ID（从任务配置中获取）
     * @return 操作结果，成功返回true
     */
    suspend fun addOrUpdateProduct(product: Product, shopId: String = ""): Boolean

    /**
     * 批量添加或更新商品监控
     * @param products 原始商品数据列表
     * @param shopId 店铺ID（从任务配置中获取）
     * @return 操作结果，成功返回true
     */
    suspend fun addOrUpdateProducts(products: List<Product>, shopId: String = ""): Boolean

    /**
     * 更新商品监控数据并记录变化（从购物车）
     * @param product 原始商品数据
     * @param shopId 店铺ID（从任务配置中获取）
     * @param shopName 店铺名称
     * @return 变化记录列表
     */
    suspend fun updateProductAndRecordChanges(
        product: Product,
        shopId: String = "",
        shopName: String = ""
    ): List<ProductChangeRecordEntity>

    /**
     * 添加或更新商品监控（从商品详情页）
     * @param productDetail 商品详情数据
     * @param shopId 店铺ID（从任务配置中获取）
     * @param shopName 店铺名称
     * @return 操作结果，成功返回true
     */
    suspend fun addOrUpdateProductFromDetail(
        productDetail: ProductDetailData,
        shopId: String = "",
        shopName: String = ""
    ): Boolean

    /**
     * 更新商品监控数据并记录变化（从商品详情页）
     * @param productDetail 商品详情数据
     * @param shopId 店铺ID（从任务配置中获取）
     * @param shopName 店铺名称
     * @return 变化记录列表
     */
    suspend fun updateProductDetailAndRecordChanges(
        productDetail: ProductDetailData,
        shopId: String = "",
        shopName: String = ""
    ): List<ProductChangeRecordEntity>

    /**
     * 删除商品监控（所有店铺）
     * @param productId 商品ID
     * @return 操作结果，成功返回true
     */
    suspend fun deleteProduct(productId: String): Boolean

    /**
     * 删除商品监控（指定店铺）
     * @param productId 商品ID
     * @param shopId 店铺ID
     * @return 操作结果，成功返回true
     */
    suspend fun deleteProductByShop(productId: String, shopId: String): Boolean

    /**
     * 获取所有商品监控
     * @return 商品监控实体列表的Flow
     */
    fun getAllProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取启用监控的商品
     * @return 启用监控的商品实体列表的Flow
     */
    fun getEnabledProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 根据商品ID获取商品监控（所有店铺）
     * @param productId 商品ID
     * @return 商品监控实体列表的Flow
     */
    fun getProductById(productId: String): Flow<List<ProductMonitorEntity>>

    /**
     * 根据商品ID和店铺ID获取商品监控
     * @param productId 商品ID
     * @param shopId 店铺ID
     * @return 商品监控实体的Flow
     */
    fun getProductByIdAndShopId(productId: String, shopId: String): Flow<ProductMonitorEntity?>

    /**
     * 搜索商品
     * @param keyword 搜索关键词
     * @return 商品监控实体列表的Flow
     */
    fun searchProducts(keyword: String): Flow<List<ProductMonitorEntity>>

    /**
     * 获取可用商品
     * @return 可用商品实体列表的Flow
     */
    fun getAvailableProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取秒杀商品
     * @return 秒杀商品实体列表的Flow
     */
    fun getSeckillProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 获取缺货商品
     * @return 缺货商品实体列表的Flow
     */
    fun getOutOfStockProducts(): Flow<List<ProductMonitorEntity>>

    /**
     * 更新商品监控状态（所有店铺）
     * @param productId 商品ID
     * @param isEnabled 是否启用监控
     * @return 操作结果，成功返回true
     */
    suspend fun updateMonitoringStatus(productId: String, isEnabled: Boolean): Boolean

    /**
     * 更新商品监控状态（指定店铺）
     * @param productId 商品ID
     * @param shopId 店铺ID
     * @param isEnabled 是否启用监控
     * @return 操作结果，成功返回true
     */
    suspend fun updateMonitoringStatusByShop(productId: String, shopId: String, isEnabled: Boolean): Boolean

    /**
     * 获取商品变化记录
     * @param productId 商品ID
     * @return 商品变化记录实体列表的Flow
     */
    fun getProductChangeRecords(
        productId: String,
        shopId: String
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取所有变化记录
     * @return 商品变化记录实体列表的Flow
     */
    fun getAllChangeRecords(): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取重要变化记录
     * @return 重要商品变化记录实体列表的Flow
     */
    fun getImportantChangeRecords(): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取指定类型的变化记录
     * @param changeType 变化类型
     * @return 商品变化记录实体列表的Flow
     */
    fun getChangeRecordsByType(changeType: ProductChangeType): Flow<List<ProductChangeRecordEntity>>

    /**
     * 获取指定时间范围内的变化记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 商品变化记录实体列表的Flow
     */
    fun getChangeRecordsByTimeRange(
        startTime: Date,
        endTime: Date
    ): Flow<List<ProductChangeRecordEntity>>

    /**
     * 清理旧的变化记录
     * @param beforeDate 清理此日期之前的记录
     * @return 删除的记录数量
     */
    suspend fun cleanOldChangeRecords(beforeDate: Date): Int

    /**
     * 获取商品监控统计信息
     * @return 统计信息Map
     */
    suspend fun getMonitoringStatistics(): Map<String, Int>
}